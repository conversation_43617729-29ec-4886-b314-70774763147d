// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'بنك الموظفين';

  @override
  String get appSubtitle => 'نظام إدارة الموظفين المصرفي';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get rememberMe => 'تذكرني';

  @override
  String get uncheckrememberme =>
      'تم إلغاء تذكر بيانات تسجيل الدخول لأن البصمة مفعلة ';

  @override
  String get validemail => 'يرجى إدخال بريد إلكتروني صحيح';

  @override
  String get correctpassword => 'كلمة المرور يجب أن تحتوي على 6 أحرف على الأقل';

  @override
  String get securityInformation => 'معلومات الأمان';

  @override
  String get rememberMeInfo =>
      'عند تفعيل هذا الخيار، سيتم حفظ بيانات تسجيل الدخول بشكل آمن على جهازك لتسهيل الدخول في المرات القادمة.\n\nيمكنك إلغاء تفعيل هذا الخيار في أي وقت من إعدادات التطبيق.';

  @override
  String get understood => 'فهمت';

  @override
  String get identification => 'الرقم الوطني';

  @override
  String get directmanager => 'المدير المباشر';

  @override
  String get quickactions => 'إجراءات سريعة';

  @override
  String get myprofile => 'الملف الشخصي';

  @override
  String get leaveTypes => 'أنواع الإجازات';

  @override
  String get myLeaveRequests => 'طلباتي';

  @override
  String get leaveApprovals => 'الموافقات';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get settings => 'الإعدادات';

  @override
  String get appSettings => 'إعدادات التطبيق';

  @override
  String get customizeSettings => 'قم بتخصيص إعدادات التطبيق حسب احتياجاتك';

  @override
  String get accountSettings => 'إعدادات الحساب';

  @override
  String get changePassword => 'تغيير كلمة المرور';

  @override
  String get changePasswordSubtitle => 'قم بتحديث كلمة المرور الخاصة بك';

  @override
  String get securitySettings => 'إعدادات الأمان';

  @override
  String get biometricLogin => 'تسجيل الدخول بالبصمة';

  @override
  String get biometricEnabledSubtitle => 'مفعل - يمكنك تسجيل الدخول بالبصمة';

  @override
  String get biometricDisabledSubtitle => 'غير مفعل - استخدم كلمة المرور فقط';

  @override
  String get appearanceSettings => 'إعدادات المظهر';

  @override
  String get changeLanguage => 'تغيير اللغة';

  @override
  String get changeLanguageSubtitle => 'اختر لغة التطبيق المفضلة لديك';

  @override
  String get selectLanguage => 'اختر اللغة';

  @override
  String get biometricEnabled => 'تم تفعيل تسجيل الدخول بالبصمة بنجاح';

  @override
  String get biometricDisabled => 'تم إلغاء تفعيل تسجيل الدخول بالبصمة';

  @override
  String get biometricSetup => 'إعداد تسجيل الدخول بالبصمة';

  @override
  String get biometricSetupMessage =>
      'لتفعيل تسجيل الدخول بالبصمة، يرجى إدخال بيانات تسجيل الدخول الخاصة بك:';

  @override
  String get enable => 'تفعيل';

  @override
  String get languageChanged => 'تم تغيير اللغة بنجاح';

  @override
  String get languageChangeError => 'حدث خطأ أثناء تغيير اللغة';

  @override
  String get cancel => 'إلغاء';

  @override
  String get enterEmail => 'أدخل البريد الإلكتروني';

  @override
  String get enterPassword => 'أدخل كلمة المرور';

  @override
  String get importantNotes => 'ملاحظات مهمة';

  @override
  String get importantNotesMessage =>
      'تأكد من أن كلمة المرور الجديدة قوية وآمنة. يجب أن تحتوي على 8 أحرف على الأقل.';

  @override
  String get updatepassword => 'تحديث كلمة المرور';

  @override
  String get currentpassword => 'كلمة المرور الحالية';

  @override
  String get currentpasswordhint => 'أدخل كلمة المرور الحالية';

  @override
  String get currentpassworderror => 'يرجى إدخال كلمة المرور الحالية';

  @override
  String get newpassword => 'كلمة المرور الجديدة';

  @override
  String get newpasswordhint => 'أدخل كلمة المرور الجديدة';

  @override
  String get confirmpassword => 'تأكيد كلمة المرور';

  @override
  String get confirmpasswordhint => 'أدخل كلمة المرور مرة أخرى للتأكيد';

  @override
  String get confirmpassworderror => 'يرجى تأكيد كلمة المرور الجديدة';

  @override
  String get passwordrequirement8 => 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';

  @override
  String get passworddoesnotmatch => 'كلمة المرور غير متطابقة';

  @override
  String get passworderror =>
      'كلمة المرور الحالية غير صحيحة. يرجى التأكد من كتابتها بشكل صحيح.';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'تم بنجاح';

  @override
  String get passwordupdatedsuccessfully => 'تم تحديث كلمة المرور بنجاح';

  @override
  String get ok => 'موافق';
}
