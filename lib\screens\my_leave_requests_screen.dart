import 'package:flutter/material.dart';
import '../services/odoo_service.dart';
import '../services/firebase_analytics_service.dart';
import '../services/performance_monitoring_service.dart';
import '../models/leave_request.dart';
import '../config/app_config.dart';
import '../generated/l10n/app_localizations.dart';

class MyLeaveRequestsScreen extends StatefulWidget {
  final OdooService odooService;
  final int uid;
  final String password;

  const MyLeaveRequestsScreen({
    super.key,
    required this.odooService,
    required this.uid,
    required this.password,
  });

  @override
  State<MyLeaveRequestsScreen> createState() => _MyLeaveRequestsScreenState();
}

class _MyLeaveRequestsScreenState extends State<MyLeaveRequestsScreen> {
  List<LeaveRequest> _leaveRequests = [];
  bool _isLoading = true;
  String? _errorMessage;
  String _currentFilter = 'all';
  bool _isFilterSectionExpanded = true;

  @override
  void initState() {
    super.initState();
    _loadLeaveRequests();

    // تتبع مشاهدة شاشة طلبات الإجازة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FirebaseAnalyticsService.logScreenView(
        screenName: 'my_leave_requests_screen',
        screenClass: 'MyLeaveRequestsScreen',
      );
    });
  }

  Future<void> _refreshData() async {
    await _loadLeaveRequests();
  }

  Future<void> _loadLeaveRequests() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    try {
      // تتبع أداء تحميل طلبات الإجازة
      final leaveRequestsData =
          await PerformanceMonitoringService.traceDatabaseOperation(
            operationType: 'read',
            tableName: 'hr.leave',
            operation: () => widget.odooService.getEmployeeLeaveRequests(
              uid: widget.uid,
              password: widget.password,
            ),
          );

      if (leaveRequestsData != null) {
        try {
          // تتبع نجاح تحميل طلبات الإجازة
          await FirebaseAnalyticsService.logEvent(
            name: 'my_leave_requests_loaded',
            parameters: {
              'requests_count': leaveRequestsData.length,
              'load_success': 'true',
            },
          );

          if (mounted) {
            setState(() {
              _leaveRequests = leaveRequestsData.map((data) {
                return LeaveRequest.fromOdooData(data);
              }).toList();
              _isLoading = false;
            });
          }
        } catch (e) {
          // تتبع خطأ معالجة البيانات
          await FirebaseAnalyticsService.logError(
            errorType: 'data_processing_error',
            errorMessage: e.toString(),
            screenName: 'my_leave_requests_screen',
            functionName: '_loadLeaveRequests',
          );

          if (mounted) {
            setState(() {
              _errorMessage = 'خطأ في معالجة البيانات: $e';
              _isLoading = false;
            });
          }
        }
      } else {
        // تتبع عدم وجود طلبات إجازة
        await FirebaseAnalyticsService.logEvent(
          name: 'my_leave_requests_empty',
          parameters: {'load_success': 'true', 'requests_count': 0},
        );

        if (mounted) {
          setState(() {
            _errorMessage = AppLocalizations.of(context).norecordsfound;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'حدث خطأ في تحميل البيانات: $e';
          _isLoading = false;
        });
      }
    }
  }

  List<LeaveRequest> _getFilteredRequests() {
    if (_currentFilter == 'all') {
      return _leaveRequests;
    } else {
      return _leaveRequests
          .where((request) => request.state == _currentFilter)
          .toList();
    }
  }

  void _applyFilter(String filterValue) {
    // تتبع تطبيق الفلتر
    FirebaseAnalyticsService.logEvent(
      name: 'leave_requests_filter_applied',
      parameters: {
        'filter_type': filterValue,
        'total_requests': _leaveRequests.length,
        'filtered_count': filterValue == 'all'
            ? _leaveRequests.length
            : _leaveRequests.where((r) => r.state == filterValue).length,
      },
    );

    if (mounted) {
      setState(() {
        _currentFilter = filterValue;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(AppConfig.lightGrayColor),
      appBar: _buildModernAppBar(context),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        color: Color(AppConfig.primaryColor),
        backgroundColor: Color(AppConfig.whiteColor),
        child: _buildBody(),
      ),
    );
  }

  PreferredSizeWidget _buildModernAppBar(BuildContext context) {
    return AppBar(
      elevation: 0,
      backgroundColor: Color(AppConfig.whiteColor),
      foregroundColor: Color(AppConfig.darkTextColor),
      toolbarHeight: 0,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_leaveRequests.isEmpty) {
      return _buildEmptyState();
    }

    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        SliverPadding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          sliver: SliverToBoxAdapter(
            child: Container(
              decoration: BoxDecoration(
                color: Color(AppConfig.whiteColor),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Color(AppConfig.cardShadowColor),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  InkWell(
                    onTap: () {
                      if (mounted) {
                        setState(() {
                          _isFilterSectionExpanded = !_isFilterSectionExpanded;
                        });
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Color(
                                      AppConfig.primaryColor,
                                    ).withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    Icons.filter_list_rounded,
                                    size: 20,
                                    color: Color(AppConfig.primaryColor),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        AppLocalizations.of(
                                          context,
                                        ).leaverequests,
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Color(AppConfig.darkTextColor),
                                        ),
                                      ),
                                      if (_currentFilter != 'all')
                                        Text(
                                          '${_getFilteredRequests().length} ${AppLocalizations.of(context).filteredrequests}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Color(
                                              AppConfig.secondaryTextColor,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Row(
                            children: [
                              if (_currentFilter != 'all')
                                IconButton(
                                  icon: Icon(Icons.clear_rounded, size: 20),
                                  onPressed: () => _applyFilter('all'),
                                  tooltip: AppLocalizations.of(context).showall,
                                  color: Color(AppConfig.errorColor),
                                ),
                              Icon(
                                _isFilterSectionExpanded
                                    ? Icons.keyboard_arrow_up_rounded
                                    : Icons.keyboard_arrow_down_rounded,
                                color: Color(AppConfig.secondaryTextColor),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (_isFilterSectionExpanded) ...[
                    Divider(height: 1, color: Color(AppConfig.dividerColor)),
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          _buildQuickStatsMini(),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: [
                                _buildFilterChip(
                                  AppLocalizations.of(context).all,
                                  'all',
                                  Icons.all_inclusive_rounded,
                                ),
                                const SizedBox(width: 8),
                                _buildFilterChip(
                                  AppLocalizations.of(context).confirm,
                                  'confirm',
                                  Icons.schedule_rounded,
                                ),
                                const SizedBox(width: 8),
                                _buildFilterChip(
                                  AppLocalizations.of(context).validate1,
                                  'validate1',
                                  Icons.thumb_up_rounded,
                                ),
                                const SizedBox(width: 8),
                                _buildFilterChip(
                                  AppLocalizations.of(context).validate,
                                  'validate',
                                  Icons.check_circle_rounded,
                                ),
                                const SizedBox(width: 8),
                                _buildFilterChip(
                                  AppLocalizations.of(context).refuse,
                                  'refuse',
                                  Icons.cancel_rounded,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Divider(height: 1, color: Color(AppConfig.dividerColor)),
                  ],
                  ..._buildFilteredRequestsList(),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildFilteredRequestsList() {
    if (_getFilteredRequests().isEmpty) {
      return [
        Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Icon(
                Icons.search_off_rounded,
                size: 48,
                color: Color(
                  AppConfig.secondaryTextColor,
                ).withValues(alpha: 0.5),
              ),
              const SizedBox(height: 12),
              Text(
                _currentFilter == 'all'
                    ? AppLocalizations.of(context).norecordsfound
                    : AppLocalizations.of(context).norecordsfound,
                style: TextStyle(
                  color: Color(AppConfig.secondaryTextColor),
                  fontSize: AppConfig.bodyFontSize,
                ),
              ),
            ],
          ),
        ),
      ];
    }

    return _getFilteredRequests().map((leaveRequest) {
      return Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: _buildModernLeaveRequestCard(
          leaveRequest,
          _getFilteredRequests().indexOf(leaveRequest),
        ),
      );
    }).toList();
  }

  Widget _buildQuickStatsMini() {
    final activeRequests = _leaveRequests
        .where((r) => ['confirm'].contains(r.state))
        .length;
    final completedRequests = _leaveRequests
        .where((r) => r.state == 'validate')
        .length;
    final rejectedRequests = _leaveRequests
        .where((r) => r.state == 'refuse')
        .length;
    final approvedRequests = _leaveRequests
        .where((r) => r.state == 'validate1')
        .length;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildStatItemMini(
            AppLocalizations.of(context).all,
            _leaveRequests.length.toString(),
            Colors.grey,
            Icons.all_inclusive_rounded,
          ),
          _buildStatItemMini(
            AppLocalizations.of(context).confirm,
            activeRequests.toString(),
            const Color(0xFFFF9800),
            Icons.schedule_rounded,
          ),
          _buildStatItemMini(
            AppLocalizations.of(context).validate,
            completedRequests.toString(),
            Colors.green,
            Icons.check_circle_rounded,
          ),
          _buildStatItemMini(
            AppLocalizations.of(context).validate1,
            approvedRequests.toString(),
            Colors.blue,
            Icons.thumb_up_rounded,
          ),
          _buildStatItemMini(
            AppLocalizations.of(context).refuse,
            rejectedRequests.toString(),
            Color(AppConfig.errorColor),
            Icons.cancel_rounded,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItemMini(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Expanded(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, size: 14, color: color),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 9,
              color: Color(AppConfig.secondaryTextColor),
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, IconData icon) {
    final bool isSelected = _currentFilter == value;
    return FilterChip(
      label: Text(label),
      avatar: Icon(icon, size: 16),
      selected: isSelected,
      showCheckmark: false,
      onSelected: (selected) => _applyFilter(selected ? value : 'all'),
      selectedColor: Color(AppConfig.primaryColor),
      checkmarkColor: Colors.white,
      labelStyle: TextStyle(
        color: isSelected ? Colors.white : Color(AppConfig.darkTextColor),
      ),
      backgroundColor: Color(AppConfig.lightGrayColor),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      side: BorderSide(
        color: isSelected
            ? Color(AppConfig.primaryColor)
            : Color(AppConfig.dividerColor),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Color(AppConfig.whiteColor),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Color(AppConfig.cardShadowColor),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                Color(AppConfig.primaryColor),
              ),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'جاري تحميل طلبات الإجازة...',
            style: TextStyle(
              fontSize: AppConfig.bodyFontSize,
              color: Color(AppConfig.secondaryTextColor),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(24),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Color(AppConfig.whiteColor),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Color(AppConfig.cardShadowColor),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Color(AppConfig.errorColor).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                Icons.error_outline_rounded,
                size: 48,
                color: Color(AppConfig.errorColor),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(
                fontSize: AppConfig.bodyFontSize,
                color: Color(AppConfig.darkTextColor),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _refreshData,
                icon: const Icon(Icons.refresh_rounded),
                label: const Text('إعادة المحاولة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Color(AppConfig.primaryColor),
                  foregroundColor: Color(AppConfig.whiteColor),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(24),
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Color(AppConfig.whiteColor),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Color(AppConfig.cardShadowColor),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Color(AppConfig.primaryColor).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                Icons.event_busy_rounded,
                size: 64,
                color: Color(AppConfig.primaryColor),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'لا توجد طلبات إجازة',
              style: TextStyle(
                fontSize: AppConfig.titleFontSize,
                fontWeight: FontWeight.bold,
                color: Color(AppConfig.darkTextColor),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم تقم بتقديم أي طلبات إجازة حتى الآن',
              style: TextStyle(
                fontSize: AppConfig.bodyFontSize,
                color: Color(AppConfig.secondaryTextColor),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernLeaveRequestCard(LeaveRequest leaveRequest, int index) {
    final stateColor =
        leaveRequest.stateText.toLowerCase().contains('قيد الانتظار')
        ? const Color(0xFFFFA500) // برتقالي
        : Color(leaveRequest.stateColor);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(20),
        onTap: () => _showLeaveRequestDetails(leaveRequest),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Color(AppConfig.whiteColor),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: const Color.fromARGB(255, 144, 206, 249),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: stateColor.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: stateColor.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      leaveRequest.stateText,
                      style: TextStyle(
                        color: stateColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: stateColor.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _getLeaveTypeIcon(leaveRequest.holidayStatusName),
                      color: stateColor,
                      size: 18,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                leaveRequest.holidayStatusName,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w800,
                  color: Color(AppConfig.darkTextColor),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color.fromARGB(255, 255, 255, 255),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: const Color.fromARGB(255, 144, 206, 249),
                    width: 2,
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context).from,
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _formatDate(leaveRequest.dateFrom),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                              color: Color(AppConfig.darkTextColor),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      child: Icon(
                        Icons.arrow_forward_rounded,
                        size: 16,
                        color: Colors.grey[400],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context).to,
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _formatDate(leaveRequest.dateTo),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                              color: Color(AppConfig.darkTextColor),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              // Duration and action
              Row(
                children: [
                  // Duration
                  Row(
                    children: [
                      Icon(
                        Icons.access_time_rounded,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 6),
                      Text(
                        '${leaveRequest.numberOfDays.toStringAsFixed(1)} ${AppLocalizations.of(context).day}',
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Color(
                        AppConfig.primaryColor,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Text(
                          AppLocalizations.of(context).show,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Color(AppConfig.primaryColor),
                          ),
                        ),
                        const SizedBox(width: 4),
                        Icon(
                          Icons.arrow_back_ios_new_rounded,
                          size: 12,
                          color: Color(AppConfig.primaryColor),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على أيقونة نوع الإجازة
  IconData _getLeaveTypeIcon(String leaveType) {
    final type = leaveType.toLowerCase();
    if (type.contains('سنوي') || type.contains('annual')) {
      return Icons.beach_access_rounded;
    } else if (type.contains('مرضي') || type.contains('sick')) {
      return Icons.local_hospital_rounded;
    } else if (type.contains('طارئ') || type.contains('emergency')) {
      return Icons.emergency_rounded;
    } else if (type.contains('أمومة') || type.contains('maternity')) {
      return Icons.child_care_rounded;
    } else if (type.contains('أبوة') || type.contains('paternity')) {
      return Icons.family_restroom_rounded;
    } else if (type.contains('دراسة') || type.contains('study')) {
      return Icons.school_rounded;
    } else {
      return Icons.event_available_rounded;
    }
  }

  /// عرض تفاصيل طلب الإجازة
  void _showLeaveRequestDetails(LeaveRequest leaveRequest) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: AppConfig.whiteColorObj,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppConfig.dividerColorObj,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // محتوى التفاصيل
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // العنوان
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Color(
                              leaveRequest.stateColor,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Icon(
                            _getLeaveTypeIcon(leaveRequest.holidayStatusName),
                            color: Color(leaveRequest.stateColor),
                            size: 24,
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'تفاصيل الطلب',
                                style: TextStyle(
                                  fontSize: AppConfig.titleFontSize,
                                  fontWeight: FontWeight.bold,
                                  color: AppConfig.darkTextColorObj,
                                ),
                              ),
                              Text(
                                leaveRequest.holidayStatusName,
                                style: TextStyle(
                                  fontSize: AppConfig.bodyFontSize,
                                  color: AppConfig.secondaryTextColorObj,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // تفاصيل الطلب
                    _buildDetailRow('الحالة', leaveRequest.stateText),
                    _buildDetailRow(
                      'تاريخ البداية',
                      _formatDate(leaveRequest.dateFrom),
                    ),
                    _buildDetailRow(
                      'تاريخ النهاية',
                      _formatDate(leaveRequest.dateTo),
                    ),
                    _buildDetailRow(
                      'عدد الأيام',
                      '${leaveRequest.numberOfDays.toStringAsFixed(1)} يوم',
                    ),

                    const Spacer(),

                    // زر الإغلاق
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('إغلاق'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صف تفصيلي
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: AppConfig.bodyFontSize,
                color: AppConfig.secondaryTextColorObj,
              ),
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: AppConfig.bodyFontSize,
                fontWeight: FontWeight.w600,
                color: AppConfig.darkTextColorObj,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
